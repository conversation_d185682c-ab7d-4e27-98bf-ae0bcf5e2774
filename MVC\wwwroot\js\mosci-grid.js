$(document).ready(function () {
    // Constants for grid limits
    const MIN_ROWS = 1;
    const MAX_ROWS = 19;

    // Function to get current row count
    function getRowCount() {
        return $('#inmateTable tbody tr').length;
    }

    // Function to validate row operations
    function validateRowOperation(operation, currentRows) {
        if (operation === 'add' && currentRows >= MAX_ROWS) {
            alert('Maximum limit of ' + MAX_ROWS + ' rows reached.');
            return false;
        }
        if (operation === 'remove' && currentRows <= MIN_ROWS) {
            alert('Minimum of ' + MIN_ROWS + ' row required.');
            return false;
        }
        return true;
    }

    // Add New Inmate button click handler
    $('#btnAddNewInmate').click(function () {
        const currentRows = getRowCount();
        if (!validateRowOperation('add', currentRows)) return;

        var newRow = $('<tr>');
        // Clone the structure of the first row
        var firstRow = $('#inmateTable tbody tr:first').clone();
        // Clear the input values
        firstRow.find('input[type="text"]').val('');
        firstRow.find('input[type="checkbox"]').prop('checked', false);
        firstRow.find('select').prop('selectedIndex', 0);
        newRow.html(firstRow.html());
        $('#inmateTable tbody').append(newRow);
    });

    // Remove Inmate button click handler
    $('button[name="btnRemoveInmate"]').click(function () {
        const currentRows = getRowCount();
        if (!validateRowOperation('remove', currentRows)) return;

        $('#inmateTable tbody tr').each(function () {
            if ($(this).find('input[name="Remove"]').prop('checked')) {
                $(this).remove();
            }
        });

        // Update row indices after removal to maintain proper form binding
        if (typeof MOSCI !== 'undefined' && typeof MOSCI.updateRowIndices === 'function') {
            MOSCI.updateRowIndices();
        }
    });

    // Delete button click handler
    $('button[name="btnDelete"]').click(function () {
        const currentRows = getRowCount();
        if (!validateRowOperation('remove', currentRows)) return;

        $('#inmateTable tbody tr').each(function () {
            if ($(this).find('input[name="Delete"]').prop('checked')) {
                $(this).remove();
            }
        });

        // Update row indices after removal to maintain proper form binding
        if (typeof MOSCI !== 'undefined' && typeof MOSCI.updateRowIndices === 'function') {
            MOSCI.updateRowIndices();
        }
    });

    // Save button click handler
    $('#btnSave').click(function () {
        var inmates = [];
        $('#inmateTable tbody tr').each(function () {
            var inmate = {
                InmateIdPrefix: $(this).find('select[name="InmateIdPrefix"], select[name*="InmateIdPrefix"]').val(),
                OffenderId: $(this).find('input[name="OffenderId"], input[name*="OffenderId"]').val(),
                LastName: $(this).find('input[name="LastName"], input[name*="LastName"]').val(),
                FirstName: $(this).find('input[name="FirstName"], input[name*="FirstName"]').val(),
                Instno: $(this).find('input[name="Instno"], input[name*="Instno"]').val(),
                ToInstitutionId: $(this).find('select[name="ToInstitutionId"], select[name*="ToInstitutionId"], select[name="SchdInst"], select[name*="SchdInst"]').val(),
                SchDate: $(this).find('input[name="SchDate"], input[name*="SchDate"]').val(),
                Descrl: $(this).find('input[name="Descrl"], input[name*="Descrl"]').val()
            };

            // Only add inmates that have some data (not completely empty rows)
            if (inmate.OffenderId || inmate.LastName || inmate.FirstName) {
                inmates.push(inmate);
            }
        });

        $('#modelJson').val(JSON.stringify(inmates));
        $('form').submit();
    });
});