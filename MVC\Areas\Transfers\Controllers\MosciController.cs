using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc;
using Odrdc.Dots.Areas.Transfers.Models.Mosci;
using Odrc.Dots.Business;
using Odrc.Dots.Business.Transfers;

namespace Odrdc.Dots.Areas.Transfers.Controllers
{
    [Area("Transfers")]
    public class MosciController : Controller
    {
        // Simulated in-memory storage for demonstration
        private static List<MosciViewModel> _mosciList = new List<MosciViewModel>();

        static MosciController()
        {
            if (_mosciList.Count == 0)
            {
                // Add sample data with different offender IDs
                _mosciList.Add(new MosciViewModel
                {
                    Recno = 1,
                    SchDate = DateTime.Now,
                    SchdInst = "MOSCI",
                    Instno = "12345",
                    Oid = "OID001",
                    Descrl = "Sample Description 1",
                    Sts = "Active",
                    Stationame = "MANCINI",
                    SysDate = DateTime.Now,
                    Romid = "R001",
                    LastName = "SMITH",
                    FirstName = "JOHN",
                    InmateIdPrefix = "A",
                    OffenderId = "123456",
                    FromInstitutionId = 1, // MANCINI (CCI)
                    ToInstitutionId = 1
                });

                _mosciList.Add(new MosciViewModel
                {
                    Recno = 2,
                    SchDate = DateTime.Now.AddDays(1),
                    SchdInst = "MOSCI",
                    Instno = "12346",
                    Oid = "OID002",
                    Descrl = "Sample Description 2",
                    Sts = "Active",
                    Stationame = "MANCINI",
                    SysDate = DateTime.Now,
                    Romid = "R002",
                    LastName = "JOHNSON",
                    FirstName = "ROBERT",
                    InmateIdPrefix = "A",
                    OffenderId = "123456",
                    FromInstitutionId = 1, // MANCINI (CCI)
                    ToInstitutionId = 2
                });

                _mosciList.Add(new MosciViewModel
                {
                    Recno = 3,
                    SchDate = DateTime.Now.AddDays(2),
                    SchdInst = "MOSCI",
                    Instno = "12347",
                    Oid = "OID003",
                    Descrl = "Sample Description 3",
                    Sts = "Active",
                    Stationame = "MANCINI",
                    SysDate = DateTime.Now,
                    Romid = "R003",
                    LastName = "WILLIAMS",
                    FirstName = "MICHAEL",
                    InmateIdPrefix = "R",
                    OffenderId = "345678",
                    FromInstitutionId = 1, // MANCINI (CCI)
                    ToInstitutionId = 3
                });

                // Add two inmates with the same offender ID but different prefixes
                _mosciList.Add(new MosciViewModel
                {
                    Recno = 4,
                    SchDate = DateTime.Now.AddDays(3),
                    SchdInst = "MOSCI",
                    Instno = "12348",
                    Oid = "OID004",
                    Descrl = "Sample Description 4",
                    Sts = "Active",
                    Stationame = "MANCINI",
                    SysDate = DateTime.Now,
                    Romid = "R004",
                    LastName = "BROWN",
                    FirstName = "DAVID",
                    InmateIdPrefix = "A",
                    OffenderId = "456789",
                    FromInstitutionId = 1, // MANCINI (CCI)
                    ToInstitutionId = 4
                });

                _mosciList.Add(new MosciViewModel
                {
                    Recno = 5,
                    SchDate = DateTime.Now.AddDays(4),
                    SchdInst = "MOSCI",
                    Instno = "12349",
                    Oid = "OID005",
                    Descrl = "Sample Description 5",
                    Sts = "Active",
                    Stationame = "MANCINI",
                    SysDate = DateTime.Now,
                    Romid = "R005",
                    LastName = "BROWN",
                    FirstName = "DAVID",
                    InmateIdPrefix = "W",
                    OffenderId = "456789",
                    FromInstitutionId = 1, // MANCINI (CCI)
                    ToInstitutionId = 5
                });
            }
        }

      //GET: Transfers/Mosci/Mosci
      //GET: Transfers/Mosci/Mosci
       [HttpGet]
        [DotsAuthorize("IMosc", true)]
        public ActionResult Mosci(string searchPrefix = "", string searchOffenderId = "", string action = "")
        {
            var model = new MosciPageViewModel();

            //Ensure dropdown  options are always populate
            LoadToDropdown(model);

            // Handle search action
            if (action == "Search" && !string.IsNullOrWhiteSpace(searchOffenderId))
            {
                model.SearchPrefix = searchPrefix ?? "A";
                model.SearchOffenderId = searchOffenderId;

                // Perform search - combine prefix and offender ID for search
                var combinedSearchId = model.SearchPrefix + searchOffenderId;
                var searchResults = PerformInmateSearchFromCombined(combinedSearchId);

                // Clear the inmates list and add search results
                model.Inmates.Clear();

                if (searchResults.Any())
                {
                    // Add all search results
                    foreach (var result in searchResults)
                    {
                        model.Inmates.Add(new MosciViewModel
                        {
                            SchDate = result.SchDate,
                            SchdInst = result.SchdInst,
                            Instno = result.Instno,
                            Oid = result.Oid,
                            Descrl = result.Descrl,
                            Mant = result.Mant,
                            Stationame = result.Stationame,
                            SysDate = result.SysDate,
                            Rowid = result.Rowid,
                            LastName = result.LastName,
                            FirstName = result.FirstName,
                            InmateIdPrefix = result.InmateIdPrefix,
                            OffenderId = result.OffenderId
                        });
                    }

                    // Add one empty row for new entries
                    //if (model.Inmates.Count < MosciPageViewModel.MaxRows)
                    //{
                    //    model.Inmates.Add(CreateEmptyInmate());
                    //}

                    model.HasSearchResults = true;
                    model.Message = $"Found {searchResults.Count} inmate(s) matching the search criteria.";
                }
                else
                {
                    // No results found - add one empty row
                    //model.Inmates.Add(CreateEmptyInmate());
                    model.HasSearchResults = false;
                    model.ErrorMessage = "No inmates found matching the search criteria.";
                }
            }
            else if (action == "AddNew")
            {
                // Add a new empty row
                if (model.Inmates.Count < MosciPageViewModel.MaxRows)
                {
                    model.Inmates.Add(CreateEmptyInmate());
                }
                else
                {
                    model.ErrorMessage = $"Maximum number of rows ({MosciPageViewModel.MaxRows}) reached. Cannot add more rows.";
                }
            }
            else
            {
                // Default view - show one empty row
                //model.Inmates.Add(CreateEmptyInmate());
            }
            LoadDefaultData(model);

            return View(model);
        }

        private void LoadToDropdown(MosciPageViewModel model)
        {
            if (model.SchdInstDrp == null || !model.SchdInstDrp.Any())
            {
                //model.SchdInstDrp = Areas.User.Utilities.HelperMethods.GetInstitutionSelectListByCodeSCHDINSTWithOutAllInsts().Where(x => x.Value != "0")
                //                            .Select(x => new SelectListItem { Value = x.Value.ToString(), Text = x.Text })
                //                            .ToList();
                model.SchdInstDrp = Areas.User.Utilities.HelperMethods.GetInstitutionSelectListByCodeSCHDINSTWithOutAllInsts()
                                            .Select(x => new SelectListItem { Value = x.Value.ToString(), Text = x.Text })
                                            .ToList();
            }
            if (model.InstnoDrp == null || !model.InstnoDrp.Any())
            {
                model.InstnoDrp = Areas.User.Utilities.HelperMethods.GetInstitutionSelectListByCodeSCHDINSTWithOutAllInsts().Where(x => x.Value != "0")
                                            .Select(x => new SelectListItem { Value = x.Value.ToString(), Text = x.Text })
                                            .ToList();
            }
        }

        // Helper method to create an empty inmate
        private MosciViewModel CreateEmptyInmate()
        {
            return new MosciViewModel
            {
                SchDate = DateTime.MinValue,
                SysDate = DateTime.Now,
                InmateIdPrefix = "", // Don't set default prefix to avoid showing "A" in empty rows
                SchdInst = 0,
                Instno = 0,
                Instname = "",
                Oid = "",
                Descrl = "",
                Mant = "",
                Stationame = "",
                Rowid = "",
                LastName = "",
                FirstName = "",
                OffenderId = "",
                
            };
            
        }

        // Helper method to perform inmate search from combined ID (prefix + offender ID)
        private List<MosciViewModel> PerformInmateSearchFromCombined(string combinedId)
        {
            var matchingInmates = new List<MosciViewModel>();

            if (!string.IsNullOrWhiteSpace(combinedId))
            {
                // Extract the prefix (first character) and the numeric part
                string prefix = "";
                string offenderId = combinedId.Trim();

                // If the search ID starts with a letter (prefix), extract it
                if (combinedId.Length > 0 && char.IsLetter(combinedId[0]))
                {
                    prefix = combinedId[0].ToString().ToUpper();
                    offenderId = combinedId.Substring(1).Trim();
                }


                var data = MosciLogic.GetMosciInfoByOaksId(combinedId);
                foreach (var mosci in data)
                {
                    matchingInmates.Add(new MosciViewModel
                    {
                        Oid = mosci.Oid,
                        SchdInst = mosci.SchdInst,
                        Instno = mosci.Instno,
                        SchDate = mosci.SchDate,
                        Descrl = mosci.Descrl,
                    });
                }
            }

            return matchingInmates;
        }

        // POST: Handle form submissions for various actions
        [HttpPost]
        [DotsAuthorize("IMosc", true)]
        public ActionResult Mosci(MosciPageViewModel model, string submitAction = "")
        {
            try
            {
                if (model.SchdInstDrp == null || !model.SchdInstDrp.Any() || model.InstnoDrp == null || !model.InstnoDrp.Any())
                {
                    LoadToDropdown(model);
                }

                LoadDefaultData(model);

       
                // For search operations, we need to clear the model state to prevent
                // the existing table data from interfering with search results
                if (submitAction == "Search")
                {
                    // Clear all model state related to Inmates to prevent binding issues
                    var keysToRemove = ModelState.Keys.Where(k => k.StartsWith("Inmates")).ToList();
                    foreach (var key in keysToRemove)
                    {
                        ModelState.Remove(key);
                    }

                    // Ensure we have a fresh model for search
                    model.Inmates = new List<MosciViewModel>();
                }

                switch (submitAction)
                {
                    case "Search":
                        return HandleSearch(model);
                    case "AddNew":
                        //return HandleAddNew(model);
                    case "RemoveSelected":
                        //return HandleRemoveSelected(model);
                    case "DeleteSelected":
                        //return HandleDeleteSelected(model);
                    case "Save":
                        return HandleSave(model);
                    case "Cancel":
                        return RedirectToAction("Index");
                    default:
                        return View(model);
                }
            }
            catch (Exception ex)
            {
                model.ErrorMessage = "An error occurred: " + ex.Message;
                return View(model);
            }
        }

        
        /// <summary>
        /// // POST: Transfers/Mosci/GetOffenderData - For JavaScript auto-population
        [HttpPost]
        public JsonResult GetOffenderData(string prefix, string offenderId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(offenderId))
                {
                    return Json(new
                    {
                        success = false,
                        message = "Offender ID cannot be empty."
                    });
                }
                var combinedOffenderId = prefix + offenderId;
                //check if the inmate is already scheduled
                var scheduleInmate = MosciLogic.GetMosciInfoByOaksId(combinedOffenderId);
                var schinmateexisting = scheduleInmate.FirstOrDefault();
                if (schinmateexisting != null && schinmateexisting.SchDate !=null)
                {
                    return Json(new
                    {
                        success = false,
                        message = "Inmate already schedule."
                    });
                }
                // Find matching offender data
                var matchingInmates = OffenderLogic.GetOffender(combinedOffenderId);

                if (matchingInmates != null)
                {
                    // Use the first matching result
                    //var foundInmate = matchingInmates.First();

                    // Find the institution ID from the dropdown options
                    var institutionId = 0;
                    var institutionOptions = Areas.User.Utilities.HelperMethods.GetInstitutionSelectListByCodeSCHDINSTWithOutAllInsts()
                        .Where(x => x.Value != "0");

                    var matchingInstitution = institutionOptions.FirstOrDefault(x => x.Text.Trim().Equals(matchingInmates.InstAbbr?.Trim(), StringComparison.OrdinalIgnoreCase));
                    if (matchingInstitution != null && int.TryParse(matchingInstitution.Value.ToString(), out institutionId))
                    {
                        // Institution ID found
                    }

                    return Json(new
                    {
                        success = true,
                        offender = new
                        {
                            lastName = matchingInmates.LastName,
                            firstName = matchingInmates.FirstName,
                            frominsText = matchingInmates.InstAbbr,
                            instno = institutionId
                        }

                    });
                }
                else
                {
                    return Json(new
                    {
                        success = false,
                        message = "No matching offender found."
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GetOffenderData error: {ex.Message}");
                return Json(new
                {
                    success = false,
                    message = "Error retrieving offender data: " + ex.Message
                });
            }
        }
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        //[DotsAuthorize("IMosc", true)]
        private ActionResult HandleSearch(MosciPageViewModel model)
        {
            if (model.SchdInstDrp == null || !model.SchdInstDrp.Any() || model.InstnoDrp == null || !model.InstnoDrp.Any())
            {
                LoadToDropdown(model);
            }

            if (string.IsNullOrWhiteSpace(model.SearchOffenderId))
            {
                model.ErrorMessage = "Please enter an Offender ID.";
                // Create a fresh list with one empty row
                //model.Inmates = new List<MosciViewModel> { CreateEmptyInmate() };
                model.Inmates = new List<MosciViewModel> ();
                model.HasSearchResults = false;
                return View(model);
            }

            // Combine prefix and offender ID for search
            var combinedSearchId = model.SearchPrefix + model.SearchOffenderId;

            var searchResults = PerformInmateSearchFromCombined(combinedSearchId);
            var offenderData = OffenderLogic.GetOffender(combinedSearchId);

            // Create a completely new list to ensure clean state
            model.Inmates = new List<MosciViewModel>();

            if (searchResults.Any() && offenderData!=null)
            {
                // Add all search results to the inmates list
                foreach (var result in searchResults)
                {
                    // Create a new instance to avoid reference issues
                    var inmateToAdd = new MosciViewModel
                    {
                        SchDate = result.SchDate,
                        SchdInst = result.SchdInst,
                        Instno = result.Instno,  // offenderData.InstAbbr -- current instituion of offender eg: 'CCI'
                        Oid = result.Oid,
                        Descrl = result.Descrl,
                        Mant = result.Mant,
                        Stationame = result.Stationame,
                        SysDate = result.SysDate,
                        Rowid = result.Rowid,
                        LastName = offenderData.LastName,
                        FirstName = offenderData.FirstName,
                        InmateIdPrefix = result.InmateIdPrefix,
                        OffenderId = model.SearchOffenderId,
                        CombinedOffenderId = combinedSearchId,
                    };

                    model.Inmates.Add(inmateToAdd);
                }
                


                // Always ensure we have at least one empty row for adding new entries
                // But don't exceed the maximum number of rows
                //if (model.Inmates.Count < MosciPageViewModel.MaxRows)
                //{
                //    model.Inmates.Add(CreateEmptyInmate());
                //}

                model.HasSearchResults = true;
                model.Message = $"Found {searchResults.Count} inmate(s) matching the search criteria.";
            }
            else if (offenderData != null)
            {
                //if mot search found bind default values of inmaete
                // Create a new instance to avoid reference issues
                var inmateToAdd = new MosciViewModel
                {
                    //SchDate = result.SchDate,
                    //SchdInst = result.SchdInst,
                    //Instno = offenderData.InstAbbr,  // offenderData.InstAbbr -- current instituion of offender eg: 'CCI'
                    Instname= offenderData.InstAbbr,
                    Oid = offenderData.Oid,
                    //Descrl = result.Descrl,
                    //Mant = result.Mant,
                    //Stationame = result.Stationame,
                    //SysDate = result.SysDate,
                    //Rowid = result.Rowid,
                    LastName = offenderData.LastName,
                    FirstName = offenderData.FirstName,
                    InmateIdPrefix = model.SearchPrefix,
                    OffenderId = model.SearchOffenderId,
                    CombinedOffenderId = combinedSearchId,
                };

                    model.Inmates.Add(inmateToAdd);
                //if (model.Inmates.Count < MosciPageViewModel.MaxRows)
                //{
                //    model.Inmates.Add(CreateEmptyInmate());
                //}

            }
            else
            {
                // No search results found - show one empty row
                //model.Inmates.Add(CreateEmptyInmate());
                model.HasSearchResults = false;
                model.ErrorMessage = "No inmates found matching the search criteria.";
            }

            // Clear ViewData to ensure clean rendering for search results
            //ViewData.Clear();

            return View(model);
        }
        [HttpPost]
        //[DotsAuthorize("IMosc", true)]
        private ActionResult HandleSave(MosciPageViewModel model)
        {
            // Verify User access
            var user = UserLogic.GetUserById(HttpContext.User.Identity.Name);
            
            foreach (var inmate in model.Inmates)
            {
                if (inmate != null)
                {
                    if (inmate.OffenderId != null && inmate.FirstName != null && inmate.LastName != null && inmate.SchdInst != null && inmate.Instno != null && inmate.SchDate != null) // New inmate
                    {
                        var combineOffenderId = inmate.InmateIdPrefix + inmate.OffenderId;
                        var offenderData = MosciLogic.GetMosciInfoByOaksId(combineOffenderId);
                        var existing = offenderData.FirstOrDefault();
                        
                        if (offenderData.Any() && existing.Rowid != null)
                        {
                            //update
                            var data = MosciLogic.InsertorUpdateMosci(combineOffenderId, Convert.ToString(inmate.SchDate), (int)inmate.Instno, (int)inmate.SchdInst, inmate.Descrl, user.UserName, existing.Rowid);
                        }
                        else
                        {
                            //insert
                            var data = MosciLogic.InsertorUpdateMosci(combineOffenderId, Convert.ToString(inmate.SchDate), (int)inmate.Instno, (int)inmate.SchdInst, inmate.Descrl, user.UserName, null);
                        }
                    }
                }
            }
            
            // Keep only the first inmate record and clear the rest
            if (model.Inmates.Count > 0)
            {
                var firstInmate = model.Inmates[0];
                model.Inmates.Clear();
                model.Inmates.Add(firstInmate);
            }
            
            model.Message = "Inmate schedule saved successfully.";
            return View(model);
        }


        [HttpPost]
        public JsonResult DeleteMosciRecord(MosciPageViewModel model)
        {
            try
            {
                // Extract the first inmate marked for deletion from the model
                var inmateToDelete = model?.Inmates?.FirstOrDefault(i => i.IsMarkedForDeletion);

                if (inmateToDelete == null)
                {
                    return Json(new
                    {
                        success = false,
                        message = "No inmate marked for deletion found in the request."
                    });
                }

                // Validate required parameters
                if (string.IsNullOrWhiteSpace(inmateToDelete.OffenderId))
                {
                    return Json(new
                    {
                        success = false,
                        message = "Offender ID cannot be empty."
                    });
                }

                if (string.IsNullOrWhiteSpace(inmateToDelete.InmateIdPrefix))
                {
                    return Json(new
                    {
                        success = false,
                        message = "Prefix cannot be empty."
                    });
                }

                if (!inmateToDelete.IsMarkedForDeletion)
                {
                    return Json(new
                    {
                        success = false,
                        message = "Record must be marked for deletion."
                    });
                }

                // Combine prefix and offender ID
                var combineOffenderId = inmateToDelete.InmateIdPrefix + inmateToDelete.OffenderId;

                if (inmateToDelete.SchDate !=null )
                {
                    var data = MosciLogic.CheckIfOffenderExists(combineOffenderId, inmateToDelete.SchDate.ToString("yyyy-MM-dd"));
                    if(data == true)
                    {
                        return Json(new
                        {
                            success = false,
                            message = "Record exist in trans3 screen"
                        });
                    }
                }

                // Get current user
                var user = UserLogic.GetUserById(HttpContext.User.Identity.Name);
                if (user == null)
                {
                    return Json(new
                    {
                        success = false,
                        message = "User not found."
                    });
                }

                

                // Get existing record to find the RowId
                var offenderData = MosciLogic.GetMosciInfoByOaksId(combineOffenderId);
                var existing = offenderData.FirstOrDefault();

                if (existing == null || string.IsNullOrWhiteSpace(existing.Rowid))
                {
                    return Json(new
                    {
                        success = false,
                        message = "Record not found or no valid Row ID."
                    });
                }

                // Delete the record
                var result = MosciLogic.DeleteMosciRecord(existing.Rowid, user.UserName);

                if (result > 0)
                {
                    return Json(new
                    {
                        success = true,
                        message = "Inmate deleted successfully from MOSCI."
                    });

                }
                else
                {
                    return Json(new
                    {
                        success = false,
                        message = "Failed to delete the record."
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in DeleteMosciRecord: {ex.Message}");
                return Json(new
                {
                    success = false,
                    message = "An error occurred while deleting the record: " + ex.Message
                });
            }
        }

        private void LoadDefaultData(MosciPageViewModel vm)
        {
            //vm.PrefixOptions = Helper.GetPrefix("0");
            vm.PrefixOptions = Helper.GetPrefix(ScreenData.DefaultInst.ToString());
            //vm.UserName = ScreenData.UserName;
        }

       
    }
}

